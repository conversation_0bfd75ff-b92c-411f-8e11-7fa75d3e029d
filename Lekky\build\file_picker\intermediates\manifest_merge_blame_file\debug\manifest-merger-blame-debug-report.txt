1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mr.flutter.plugin.filepicker" >
4
5    <uses-sdk
6        android:minSdkVersion="16"
6-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml
7        android:targetSdkVersion="16" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml
8
9    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:3:5-79
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:3:22-77
10
11    <queries>
11-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:4:5-9:15
12        <intent>
12-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:5:6-8:14
13            <action android:name="android.intent.action.GET_CONTENT" />
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:6:9-68
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:6:17-65
14
15            <data android:mimeType="*/*" />
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:7:9-39
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\src\main\AndroidManifest.xml:7:15-37
16        </intent>
17    </queries>
18
19</manifest>
