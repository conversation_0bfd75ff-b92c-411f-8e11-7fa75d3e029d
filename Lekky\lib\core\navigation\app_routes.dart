// File: lib/core/navigation/app_routes.dart
import '../constants/app_constants.dart';

/// Enum representing all possible routes in the app
enum AppRoute {
  // Main Routes
  splash,
  welcome,
  setup,
  home,
  validationDashboard,

  // Main Tab Routes
  history,
  cost,
  mainSettings,

  // Debug Routes
  debugNotifications;
}

/// Extension to convert AppRoute to path string
extension AppRouteExtension on AppRoute {
  String get path {
    switch (this) {
      case AppRoute.splash:
        return AppConstants.routeSplash;
      case AppRoute.welcome:
        return AppConstants.routeWelcome;
      case AppRoute.setup:
        return AppConstants.routeSetup;
      case AppRoute.home:
        return AppConstants.routeHome;
      case AppRoute.validationDashboard:
        return AppConstants.routeValidationDashboard;
      case AppRoute.history:
        return AppConstants.routeHistory;
      case AppRoute.cost:
        return AppConstants.routeCost;
      case AppRoute.mainSettings:
        return AppConstants.routeMainSettings;

      case AppRoute.debugNotifications:
        return AppConstants.routeDebugNotifications;
    }
  }

  /// Get route name for go_router
  String get name {
    switch (this) {
      case AppRoute.splash:
        return 'splash';
      case AppRoute.welcome:
        return 'welcome';
      case AppRoute.setup:
        return 'setup';
      case AppRoute.home:
        return 'home';
      case AppRoute.validationDashboard:
        return 'validation-dashboard';
      case AppRoute.history:
        return 'history';
      case AppRoute.cost:
        return 'cost';
      case AppRoute.mainSettings:
        return 'main-settings';

      case AppRoute.debugNotifications:
        return 'debug-notifications';
    }
  }
}

/// Route validation utilities
class RouteValidator {
  /// Validate route parameters
  static bool validateRoute(AppRoute route) {
    // All routes are valid for now
    // Add specific validation logic as needed
    return true;
  }
}
