// File: lib/core/constants/app_constants.dart
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // Main Routes
  static const String routeHome = '/home';
  static const String routeWelcome = '/welcome';
  static const String routeSplash = '/splash';
  static const String routeSetup = '/setup';
  static const String routeValidationDashboard = '/validation-dashboard';

  // Main Tab Routes
  static const String routeHistory = '/history';
  static const String routeCost = '/cost';
  static const String routeMainSettings = '/main-settings';

  // Debug Routes
  static const String routeDebugNotifications = '/debug/notifications';

  // SharedPreferences keys
  static const String keySetupCompleted = 'setup_completed';
}
