{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-18:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,1212", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,1308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "790,895,999,1104", "endColumns": "104,103,104,107", "endOffsets": "890,994,1099,1207"}}]}]}