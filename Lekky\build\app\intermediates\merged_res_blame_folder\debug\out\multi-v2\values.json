{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-18:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8adab4e681d0863a25dfe4be8db95bf\\transformed\\jetified-window-1.0.0-beta04\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,91,151,157,270,278,290", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "115,294,354,406,5560,9299,9494,13390,13672,14112", "endLines": "7,8,9,10,91,156,160,277,289,297", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "289,349,401,446,5615,9489,9620,13667,14107,14416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35761e94c6d5258a9b643a9e38a6ce19\\transformed\\jetified-startup-runtime-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6722", "endColumns": "82", "endOffsets": "6800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "17,18,19,20,28,29,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "871,929,995,1058,1616,1687,7316,7384,7451,7530", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "924,990,1053,1115,1682,1754,7379,7446,7525,7594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5c275d7683859a49fefc7284389acf42\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "25,26,27,52,53,54,55,111,138,140,141,146,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1383,1472,1543,3225,3278,3331,3384,6592,8338,8514,8636,8898,9093", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "1467,1538,1611,3273,3326,3379,3432,6647,8399,8631,8692,8959,9155"}}, {"source": "D:\\000.Workspace\\Lekky\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "126,130", "startColumns": "4,4", "startOffsets": "7670,7851", "endLines": "129,132", "endColumns": "12,12", "endOffsets": "7846,8015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2a5a688d87ded2dd8a90eb4f6288faf8\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "451,516,586,650", "endColumns": "64,69,63,60", "endOffsets": "511,581,645,706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5389d6eda3e5eba8ba922c509419fb7d\\transformed\\lifecycle-runtime-2.3.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6549", "endColumns": "42", "endOffsets": "6587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,15,16,21,22,23,24,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,112,114,115,116,117,118,119,120,125,133,134,139,142,147,149,150,161,167,177,210,231,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,711,783,1120,1185,1251,1320,1759,1829,1897,1969,2039,2100,2174,2247,2308,2369,2431,2495,2557,2618,2686,2786,2846,2912,2985,3054,3111,3163,3437,3509,3585,3650,3709,3768,3828,3888,3948,4008,4068,4128,4188,4248,4308,4368,4427,4487,4547,4607,4667,4727,4787,4847,4907,4967,5027,5086,5146,5206,5265,5324,5383,5442,5501,5620,5655,5690,5745,5808,5863,5921,5979,6040,6103,6160,6211,6261,6322,6379,6445,6479,6514,6652,6805,6872,6944,7013,7082,7156,7228,7599,8020,8137,8404,8697,8964,9160,9232,9625,9828,10129,11860,12541,13223", "endLines": "2,15,16,21,22,23,24,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,112,114,115,116,117,118,119,120,125,133,137,139,145,147,149,150,166,176,209,230,263,269", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,778,866,1180,1246,1315,1378,1824,1892,1964,2034,2095,2169,2242,2303,2364,2426,2490,2552,2613,2681,2781,2841,2907,2980,3049,3106,3158,3220,3504,3580,3645,3704,3763,3823,3883,3943,4003,4063,4123,4183,4243,4303,4363,4422,4482,4542,4602,4662,4722,4782,4842,4902,4962,5022,5081,5141,5201,5260,5319,5378,5437,5496,5555,5650,5685,5740,5803,5858,5916,5974,6035,6098,6155,6206,6256,6317,6374,6440,6474,6509,6544,6717,6867,6939,7008,7077,7151,7223,7311,7665,8132,8333,8509,8893,9088,9227,9294,9823,10124,11855,12536,13218,13385"}}]}]}