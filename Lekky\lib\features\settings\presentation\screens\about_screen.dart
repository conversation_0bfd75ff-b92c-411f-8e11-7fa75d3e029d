import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/providers/settings_navigation_provider.dart';

/// About screen
class AboutScreen extends StatelessWidget {
  /// Constructor
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Banner with back arrow
          GestureDetector(
            onTap: () => context
                .go('/main-settings?expanded=${SettingsCategoryIndex.about}'),
            child: AppBanner(
              message: '← App Information',
              gradientColors: AppColors.getSettingsMainCardGradient(
                  Theme.of(context).brightness == Brightness.dark),
              textColor: AppColors.getAppBarTextColor(
                  'settings', Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // App Info section
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue),
                            SizedBox(width: 16),
                            Text(
                              'App Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // App logo
                        Center(
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.electric_meter,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // App name and version
                        const Center(
                          child: Text(
                            'Lekky',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Center(
                          child: Text(
                            'Version 1.0.0',
                            style: TextStyle(
                              fontSize: 16,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // App description
                        const Text(
                          'Lekky is a prepaid electricity meter tracking app designed to help you monitor your electricity usage and manage your meter readings.',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                // Features section
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber),
                            SizedBox(width: 16),
                            Text(
                              'Features',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Features list
                        _buildFeatureItem(
                          Icons.track_changes,
                          'Track Meter Readings',
                          'Record and monitor your meter readings over time',
                        ),
                        _buildFeatureItem(
                          Icons.add_card,
                          'Record Top-ups',
                          'Keep track of all your meter top-ups',
                        ),
                        _buildFeatureItem(
                          Icons.calculate,
                          'Usage Calculations',
                          'Automatically calculate your daily usage',
                        ),
                        _buildFeatureItem(
                          Icons.notifications,
                          'Smart Notifications',
                          'Get alerts when your balance is running low',
                        ),
                        _buildFeatureItem(
                          Icons.history,
                          'History Tracking',
                          'View your complete usage history',
                        ),
                        _buildFeatureItem(
                          Icons.backup,
                          'Data Backup',
                          'Backup and restore your data',
                        ),
                      ],
                    ),
                  ),
                ),

                // Contact & Support section
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.contact_support, color: Colors.blue),
                            SizedBox(width: 16),
                            Text(
                              'Contact & Support',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Contact info
                        const Text(
                          'If you have any questions, suggestions, or issues, please contact us. Please note that I am a sole developer with limited resources and this is a hobby project:',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Email contact
                        _buildContactItem(
                          icon: Icons.email,
                          title: 'Email Support',
                          subtitle: '<EMAIL>',
                          onTap: () => _launchEmail('<EMAIL>'),
                        ),

                        const SizedBox(height: 12),

                        // GitHub contact
                        _buildContactItem(
                          icon: Icons.code,
                          title: 'GitHub Issues',
                          subtitle: 'Report bugs or request features',
                          onTap: () =>
                              _launchUrl('https://github.com/lekky/issues'),
                        ),

                        const SizedBox(height: 12),

                        // Website contact
                        _buildContactItem(
                          icon: Icons.web,
                          title: 'Website',
                          subtitle: 'www.lekky.app',
                          onTap: () => _launchUrl('https://www.lekky.app'),
                        ),

                        const SizedBox(height: 16),

                        // Support note
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.blue.withOpacity(0.3),
                            ),
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.info, color: Colors.blue, size: 20),
                              SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'We will attempt to respond to support requests ASAP.',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Copyright
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        '© 2025 Lekky App. All rights reserved.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a contact item
  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Icon(icon, color: Colors.blue),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  /// Launch email
  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Lekky App Support',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  /// Launch URL
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
